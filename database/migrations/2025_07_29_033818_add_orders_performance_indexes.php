<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Adds performance indexes for the optimized orders query.
     * These indexes are crucial for both MySQL and PostgreSQL performance.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Individual indexes for filtering
            $table->index('user_id', 'idx_orders_user_id');
            $table->index('vendor_id', 'idx_orders_vendor_id');
            $table->index('payment_status', 'idx_orders_payment_status');
            $table->index('fulfillment_status', 'idx_orders_fulfillment_status');
            $table->index('created_at', 'idx_orders_created_at');
            $table->index('is_active', 'idx_orders_is_active');
            
            // Composite indexes for common filter combinations
            $table->index(['is_active', 'created_at'], 'idx_orders_active_created');
            $table->index(['is_active', 'user_id'], 'idx_orders_active_user');
            $table->index(['is_active', 'vendor_id'], 'idx_orders_active_vendor');
            $table->index(['is_active', 'payment_status'], 'idx_orders_active_payment');
            $table->index(['is_active', 'fulfillment_status'], 'idx_orders_active_fulfillment');
            
            // Index for search functionality
            $table->index('order_number', 'idx_orders_order_number');
        });

        Schema::table('order_items', function (Blueprint $table) {
            // Critical index for withCount and withSum operations
            $table->index('order_id', 'idx_order_items_order_id');
            
            // Composite index for quantity aggregations
            $table->index(['order_id', 'quantity'], 'idx_order_items_order_quantity');
        });

        Schema::table('users', function (Blueprint $table) {
            // Indexes for user search and joins
            $table->index('name', 'idx_users_name');
            $table->index('email', 'idx_users_email');
        });

        Schema::table('vendors', function (Blueprint $table) {
            // Index for vendor joins and search
            $table->index('vendor_display_name_en', 'idx_vendors_display_name_en');
            $table->index('is_active', 'idx_vendors_is_active');
        });

        // PostgreSQL specific optimizations
        if (DB::connection()->getDriverName() === 'pgsql') {
            // Create partial indexes for PostgreSQL (more efficient)
            DB::statement('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_active_only ON orders (created_at DESC) WHERE is_active = true');
            DB::statement('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_paid_only ON orders (created_at DESC) WHERE payment_status = \'paid\' AND is_active = true');
            DB::statement('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_pending_only ON orders (created_at DESC) WHERE fulfillment_status = \'pending\' AND is_active = true');
            
            // Text search index for order numbers and customer info
            DB::statement('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_search ON orders USING gin(to_tsvector(\'english\', order_number))');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_user_id');
            $table->dropIndex('idx_orders_vendor_id');
            $table->dropIndex('idx_orders_payment_status');
            $table->dropIndex('idx_orders_fulfillment_status');
            $table->dropIndex('idx_orders_created_at');
            $table->dropIndex('idx_orders_is_active');
            $table->dropIndex('idx_orders_active_created');
            $table->dropIndex('idx_orders_active_user');
            $table->dropIndex('idx_orders_active_vendor');
            $table->dropIndex('idx_orders_active_payment');
            $table->dropIndex('idx_orders_active_fulfillment');
            $table->dropIndex('idx_orders_order_number');
        });

        Schema::table('order_items', function (Blueprint $table) {
            $table->dropIndex('idx_order_items_order_id');
            $table->dropIndex('idx_order_items_order_quantity');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_name');
            $table->dropIndex('idx_users_email');
        });

        Schema::table('vendors', function (Blueprint $table) {
            $table->dropIndex('idx_vendors_display_name_en');
            $table->dropIndex('idx_vendors_is_active');
        });

        // Drop PostgreSQL specific indexes
        if (DB::connection()->getDriverName() === 'pgsql') {
            DB::statement('DROP INDEX CONCURRENTLY IF EXISTS idx_orders_active_only');
            DB::statement('DROP INDEX CONCURRENTLY IF EXISTS idx_orders_paid_only');
            DB::statement('DROP INDEX CONCURRENTLY IF EXISTS idx_orders_pending_only');
            DB::statement('DROP INDEX CONCURRENTLY IF EXISTS idx_orders_search');
        }
    }
};
